# Cấu hình môi trường cho Sharehost
# Copy file này thành .env và điền thông tin thực tế

# ======================
# SERVER CONFIGURATION
# ======================
HOST=0.0.0.0
PORT=1337
NODE_ENV=production

# ======================
# DATABASE CONFIGURATION
# ======================
DATABASE_CLIENT=mysql
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=your_database_name
DATABASE_USERNAME=your_username
DATABASE_PASSWORD=your_password

# Nếu sử dụng SSL cho database (tùy chọn)
DATABASE_SSL=false

# ======================
# STRAPI SECRETS
# ======================
# Tạo các key ngẫu nhiên cho bảo mật
# Có thể sử dụng: openssl rand -base64 32

APP_KEYS=your_app_key_1,your_app_key_2,your_app_key_3,your_app_key_4
API_TOKEN_SALT=your_api_token_salt_here
ADMIN_JWT_SECRET=your_admin_jwt_secret_here
TRANSFER_TOKEN_SALT=your_transfer_token_salt_here
JWT_SECRET=your_jwt_secret_here

# ======================
# OPTIONAL SETTINGS
# ======================

# Admin panel (có thể tắt trong production)
SERVE_ADMIN=true

# File upload settings
MAX_FILE_SIZE=50000000

# CORS settings (nếu cần)
# CORS_ORIGIN=https://yourdomain.com

# Rate limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_DURATION=60000

# ======================
# LOGGING
# ======================
LOG_LEVEL=info

# ======================
# PERFORMANCE
# ======================
# Memory settings cho Node.js
NODE_OPTIONS=--max-old-space-size=1024
